#!/usr/bin/env python3
"""
Interactive Test Script for Streaming JSON Reader Extension

This script loads DuckD<PERSON> with the streaming JSON reader extension and provides
an interactive SQL shell for testing queries.
"""

import duckdb
import tempfile
import json
import os
import sys
from pathlib import Path


def create_sample_files():
    """Create sample JSON files for testing."""
    samples = {}
    
    # Simple object
    simple_data = {"name": "<PERSON>", "age": 30, "city": "NYC"}
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(simple_data, f)
        samples['simple'] = f.name
    
    # Array of objects
    array_data = [
        {"id": 1, "name": "Alice", "department": "Engineering"},
        {"id": 2, "name": "<PERSON>", "department": "Sales"},
        {"id": 3, "name": "Charlie", "department": "Marketing"}
    ]
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(array_data, f)
        samples['array'] = f.name
    
    # Nested structure
    nested_data = {
        "company": "TechCorp",
        "employees": [
            {
                "id": 1,
                "name": "<PERSON>",
                "profile": {
                    "age": 30,
                    "skills": ["Python", "SQL", "JavaScript"]
                },
                "projects": [
                    {"name": "Project A", "status": "active"},
                    {"name": "Project B", "status": "completed"}
                ]
            },
            {
                "id": 2,
                "name": "Bob",
                "profile": {
                    "age": 25,
                    "skills": ["Java", "React"]
                },
                "projects": [
                    {"name": "Project C", "status": "planning"}
                ]
            }
        ]
    }
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(nested_data, f)
        samples['nested'] = f.name
    
    # Unicode test
    unicode_data = {
        "name": "José María",
        "city": "São Paulo",
        "message": "Hello 👋 World 🌍",
        "languages": ["English", "Português", "Español", "中文", "العربية"]
    }
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        json.dump(unicode_data, f, ensure_ascii=False)
        samples['unicode'] = f.name
    
    return samples


def print_banner():
    """Print welcome banner."""
    print("=" * 60)
    print("🚀 Streaming JSON Reader Extension - Interactive Test")
    print("=" * 60)
    print()


def print_help(samples):
    """Print help information."""
    print("📋 Available Commands:")
    print("  help          - Show this help")
    print("  samples       - List sample files")
    print("  quit/exit     - Exit the program")
    print("  <SQL query>   - Execute SQL query")
    print()
    print("📁 Sample Files Created:")
    for name, path in samples.items():
        print(f"  {name:10} - {path}")
    print()
    print("💡 Example Queries:")
    print(f"  SELECT * FROM streaming_json_reader('{samples['simple']}');")
    print(f"  SELECT COUNT(*) FROM streaming_json_reader('{samples['array']}');")
    print(f"  DESCRIBE SELECT * FROM streaming_json_reader('{samples['nested']}');")
    print()
    print("🔍 Test Error Handling:")
    print("  SELECT * FROM streaming_json_reader('/nonexistent/file.json');")
    print()


def execute_query(conn, query):
    """Execute a SQL query and display results."""
    try:
        # Execute the query
        result = conn.execute(query)
        
        # Get column names
        columns = [desc[0] for desc in result.description] if result.description else []
        
        # Fetch all results
        rows = result.fetchall()
        
        # Display results
        if not rows:
            print("✅ Query executed successfully (0 rows returned)")
            return
        
        # Print header
        if columns:
            header = " | ".join(f"{col:20}" for col in columns)
            print(header)
            print("-" * len(header))
        
        # Print rows
        for row in rows:
            if len(str(row[0])) > 100:
                # Truncate long JSON strings for readability
                truncated = str(row[0])[:100] + "..." if len(str(row[0])) > 100 else str(row[0])
                row_str = " | ".join(f"{truncated:20}")
            else:
                row_str = " | ".join(f"{str(cell):20}" for cell in row)
            print(row_str)
        
        print(f"\n✅ {len(rows)} row(s) returned")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def cleanup_samples(samples):
    """Clean up sample files."""
    for path in samples.values():
        try:
            os.unlink(path)
        except:
            pass


def main():
    """Main interactive loop."""
    print_banner()
    
    # Check if extension exists
    extension_path = "./build/debug/streaming_json_reader.duckdb_extension"
    if not Path(extension_path).exists():
        print(f"❌ Extension not found at: {extension_path}")
        print("   Please build the extension first with: make debug")
        sys.exit(1)
    
    # Create DuckDB connection and load extension
    try:
        print("🔌 Connecting to DuckDB...")
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        print("📦 Loading streaming JSON reader extension...")
        conn.execute(f'LOAD "{extension_path}"')
        
        print("✅ Extension loaded successfully!")
        print()
        
    except Exception as e:
        print(f"❌ Failed to load extension: {e}")
        sys.exit(1)
    
    # Create sample files
    print("📁 Creating sample JSON files...")
    samples = create_sample_files()
    print("✅ Sample files created!")
    print()
    
    # Show help
    print_help(samples)
    
    try:
        # Interactive loop
        while True:
            try:
                query = input("🔍 SQL> ").strip()
                
                if not query:
                    continue
                
                if query.lower() in ['quit', 'exit']:
                    break
                
                if query.lower() == 'help':
                    print_help(samples)
                    continue
                
                if query.lower() == 'samples':
                    print("📁 Sample Files:")
                    for name, path in samples.items():
                        print(f"  {name:10} - {path}")
                    print()
                    continue
                
                # Execute the query
                print()
                execute_query(conn, query)
                print()
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except EOFError:
                print("\n👋 Goodbye!")
                break
    
    finally:
        # Cleanup
        print("🧹 Cleaning up sample files...")
        cleanup_samples(samples)
        conn.close()


if __name__ == '__main__':
    main()
