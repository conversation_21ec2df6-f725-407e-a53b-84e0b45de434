#!/usr/bin/env python3
"""
Manual test script to verify the streaming JSON reader functionality.
"""

import duckdb
import json
import tempfile
import os

def test_extension():
    """Test the streaming JSON reader extension manually."""
    
    # Connect to DuckDB and load extension
    conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
    conn.execute('LOAD "./build/debug/streaming_json_reader.duckdb_extension"')
    print("✅ Extension loaded successfully")
    
    # Test 1: Simple JSON with users
    print("\n📋 Test 1: Simple JSON with users")
    simple_data = {
        "users": [
            {"id": 1, "name": "Alice", "department": "Engineering"},
            {"id": 2, "name": "Bob", "department": "Design"}
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(simple_data, f)
        simple_file = f.name
    
    try:
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{simple_file}")').fetchall()
        print(f"✅ Simple test: {len(result)} rows")
        for i, row in enumerate(result):
            print(f"  Row {i+1}: {row}")
    finally:
        os.unlink(simple_file)
    
    # Test 2: Nested JSON with users and projects
    print("\n📋 Test 2: Nested JSON with users and projects")
    nested_data = {
        "metadata": {"name": "Test Dataset"},
        "users": [
            {
                "id": 1,
                "name": "Alice",
                "department": "Engineering",
                "projects": [
                    {"name": "Project Alpha", "status": "completed", "budget": 50000},
                    {"name": "Project Beta", "status": "in_progress", "budget": 75000}
                ]
            },
            {
                "id": 2,
                "name": "Bob",
                "department": "Design", 
                "projects": [
                    {"name": "Project Gamma", "status": "planning", "budget": 30000},
                    {"name": "Project Delta", "status": "completed", "budget": 45000}
                ]
            }
        ]
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(nested_data, f)
        nested_file = f.name
    
    try:
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{nested_file}")').fetchall()
        print(f"✅ Nested test: {len(result)} rows")
        for i, row in enumerate(result):
            print(f"  Row {i+1}: {row}")
    finally:
        os.unlink(nested_file)
    
    # Test 3: Test with existing test files
    print("\n📋 Test 3: Using existing test files")
    
    if os.path.exists("test_multiply_nested.json"):
        result = conn.execute('SELECT * FROM streaming_json_reader("test_multiply_nested.json")').fetchall()
        print(f"✅ test_multiply_nested.json: {len(result)} rows")
        for i, row in enumerate(result[:3]):
            print(f"  Row {i+1}: {row}")
        if len(result) > 3:
            print(f"  ... and {len(result) - 3} more rows")
    
    print("\n🎉 All manual tests completed!")

if __name__ == "__main__":
    test_extension()
