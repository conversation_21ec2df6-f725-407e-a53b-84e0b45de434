#!/usr/bin/env python3
"""
Simple test script - loads DuckD<PERSON> with the streaming JSON reader extension
and lets you run SQL queries interactively.
"""

import duckdb

# Load DuckDB and extension
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "./build/debug/streaming_json_reader.duckdb_extension"')

print("Extension loaded. Enter SQL queries (Ctrl+C to exit):")

# Interactive SQL loop
while True:
    try:
        query = input("SQL> ")
        if query.strip():
            result = conn.execute(query).fetchall()
            print(result)
    except KeyboardInterrupt:
        break
    except Exception as e:
        print(f"Error: {e}")

conn.close()
