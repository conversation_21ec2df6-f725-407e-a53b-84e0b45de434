"""
    pygments.lexers.math
    ~~~~~~~~~~~~~~~~~~~~

    Just export lexers that were contained in this module.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

# ruff: noqa: F401
from pygments.lexers.python import NumPyLexer
from pygments.lexers.matlab import MatlabLexer, MatlabSessionLexer, \
    OctaveLexer, ScilabLexer
from pygments.lexers.julia import <PERSON><PERSON><PERSON><PERSON>, JuliaConsoleLexer
from pygments.lexers.r import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RdLexer
from pygments.lexers.modeling import <PERSON><PERSON><PERSON><PERSON>, <PERSON>ags<PERSON>ex<PERSON>, StanLexer
from pygments.lexers.idl import IDLLexer
from pygments.lexers.algebra import MuPA<PERSON><PERSON>exer

__all__ = []
