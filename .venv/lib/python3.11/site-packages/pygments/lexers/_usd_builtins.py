"""
    pygments.lexers._usd_builtins
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

    A collection of known USD-related keywords, attributes, and types.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

COMMON_ATTRIBUTES = [
    "extent",
    "xformOpOrder",
]

KEYWORDS = [
    "class",
    "clips",
    "custom",
    "customData",
    "def",
    "dictionary",
    "inherits",
    "over",
    "payload",
    "references",
    "rel",
    "subLayers",
    "timeSamples",
    "uniform",
    "variantSet",
    "variantSets",
    "variants",
]

OPERATORS = [
    "add",
    "append",
    "delete",
    "prepend",
    "reorder",
]

SPECIAL_NAMES = [
    "active",
    "apiSchemas",
    "defaultPrim",
    "elementSize",
    "endTimeCode",
    "hidden",
    "instanceable",
    "interpolation",
    "kind",
    "startTimeCode",
    "upAxis",
]

TYPES = [
    "asset",
    "bool",
    "color3d",
    "color3f",
    "color3h",
    "color4d",
    "color4f",
    "color4h",
    "double",
    "double2",
    "double3",
    "double4",
    "float",
    "float2",
    "float3",
    "float4",
    "frame4d",
    "half",
    "half2",
    "half3",
    "half4",
    "int",
    "int2",
    "int3",
    "int4",
    "keyword",
    "matrix2d",
    "matrix3d",
    "matrix4d",
    "normal3d",
    "normal3f",
    "normal3h",
    "point3d",
    "point3f",
    "point3h",
    "quatd",
    "quatf",
    "quath",
    "string",
    "syn",
    "token",
    "uchar",
    "uchar2",
    "uchar3",
    "uchar4",
    "uint",
    "uint2",
    "uint3",
    "uint4",
    "usdaType",
    "vector3d",
    "vector3f",
    "vector3h",
]
