from ... import DuckDBPyConnection
from ...typing import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Any

class NullValue(Value):
    def __init__(self) -> None: ...
    def __repr__(self) -> str: ...

class BooleanValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class UnsignedBinaryValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class UnsignedShortValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class UnsignedIntegerValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class UnsignedLongValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class BinaryValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class ShortValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class IntegerValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class LongValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class HugeIntegerValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class FloatValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class DoubleValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class DecimalValue(Value):
        def __init__(self, object: Any, width: int, scale: int) -> None: ...
        def __repr__(self) -> str: ...
    
class StringValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class UUIDValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class BitValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class BlobValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class DateValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class IntervalValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimestampValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimestampSecondValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimestampMilisecondValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimestampNanosecondValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimestampTimeZoneValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimeValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...

class TimeTimeZoneValue(Value):
    def __init__(self, object: Any) -> None: ...
    def __repr__(self) -> str: ...


class Value:
    def __init__(self, object: Any, type: DuckDBPyType) -> None: ...
    def __repr__(self) -> str: ...
