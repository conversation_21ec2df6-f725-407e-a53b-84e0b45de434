from duckdb.duckdb.typing import (
    DuckDBPyType,
    BIGINT,
    BIT,
    BLOB,
    BOOLEAN,
    DATE,
    DOUBLE,
    FLOAT,
    HUGEINT,
    UHUGEINT,
    INTEGER,
    INTERVAL,
    SMALLINT,
    <PERSON><PERSON>NULL,
    TIM<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_MS,
    <PERSON><PERSON><PERSON><PERSON>MP_NS,
    TIM<PERSON><PERSON>MP_S,
    TIMES<PERSON>MP_TZ,
    TIME_TZ,
    TINYINT,
    UBIGINT,
    UINTEGER,
    USMALLINT,
    UTINYINT,
    UUID,
    VARCHAR
)

__all__ = [
    "DuckDBPyType",
    "BIGINT",
    "BIT",
    "BLOB",
    "BO<PERSON>EAN",
    "DATE",
    "DOUBL<PERSON>",
    "FLOAT",
    "HUGEINT",
    "UHU<PERSON>INT",
    "INTEGER",
    "INTERVAL",
    "SMALLINT",
    "SQLNULL",
    "TIME",
    "TIMES<PERSON><PERSON>",
    "TIMES<PERSON><PERSON>_<PERSON>",
    "TIMES<PERSON>MP_NS",
    "TIMES<PERSON>MP_S",
    "TIMES<PERSON>MP_TZ",
    "TIME_TZ",
    "TINYINT",
    "UBIGI<PERSON>",
    "<PERSON>IN<PERSON>GE<PERSON>",
    "<PERSON>MALLINT",
    "UTINYINT",
    "UUID",
    "VARCHAR"
]
