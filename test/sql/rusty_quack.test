# name: test/sql/streaming_json_reader.test
# description: test streaming_json_reader extension
# group: [json]

# Before we load the extension, this will fail
statement error
SELECT * FROM streaming_json_reader('test.json');
----
Catalog Error: Table Function with name streaming_json_reader does not exist!

# Require statement will ensure the extension is loaded from now on
require streaming_json_reader

require icu

# Confirm the extension works with a simple test
query I
SELECT * FROM streaming_json_reader('test.json');
----
JSON Reader for file: test.json