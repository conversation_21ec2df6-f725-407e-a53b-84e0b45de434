#!/usr/bin/env python3
"""
Critical Assessment Test Runner

This script runs the comprehensive critical assessment of the Streaming JSON Reader extension.
It executes all test categories and generates a detailed report of findings.
"""

import subprocess
import sys
import time
import json
import os
from pathlib import Path
from datetime import datetime


class CriticalAssessmentRunner:
    """Main runner for the critical assessment test suite."""
    
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'test_categories': {},
            'summary': {},
            'critical_findings': [],
            'recommendations': []
        }
        
        self.test_modules = [
            ('Memory Safety', 'memory_safety_tests.py'),
            ('Performance Characteristics', 'performance_tests.py'),
            ('Edge Cases & Error Handling', 'edge_case_tests.py'),
            ('Security Vulnerabilities', 'security_tests.py'),
            ('Scalability & Resource Limits', 'scalability_tests.py')
        ]
    
    def check_prerequisites(self):
        """Check that all prerequisites are met before running tests."""
        print("🔍 Checking prerequisites...")
        
        # Check if extension is built
        extension_path = Path("../build/debug/streaming_json_reader.duckdb_extension")
        if not extension_path.exists():
            print("❌ Extension not found. Please run 'make debug' first.")
            return False
        
        # Check required Python packages
        required_packages = ['pytest', 'duckdb', 'psutil']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing required packages: {', '.join(missing_packages)}")
            print("Please install with: pip install " + " ".join(missing_packages))
            return False
        
        print("✅ All prerequisites met")
        return True
    
    def run_test_category(self, category_name, test_file):
        """Run a specific test category and capture results."""
        print(f"\n{'='*60}")
        print(f"🧪 Running {category_name} Tests")
        print(f"{'='*60}")
        
        test_path = Path(test_file)

        if not test_path.exists():
            print(f"❌ Test file not found: {test_path}")
            return False
        
        start_time = time.time()
        
        try:
            # Run pytest with verbose output and capture results
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                str(test_path), 
                '-v', '-s', '--tb=short',
                '--json-report', '--json-report-file=temp_results.json'
            ], capture_output=True, text=True, timeout=1800)  # 30 minute timeout
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # Parse results
            test_results = {
                'execution_time': execution_time,
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'passed': 0,
                'failed': 0,
                'errors': 0,
                'skipped': 0
            }
            
            # Try to parse JSON report if available
            if Path('temp_results.json').exists():
                try:
                    with open('temp_results.json', 'r') as f:
                        json_results = json.load(f)
                        test_results.update({
                            'passed': json_results.get('summary', {}).get('passed', 0),
                            'failed': json_results.get('summary', {}).get('failed', 0),
                            'errors': json_results.get('summary', {}).get('error', 0),
                            'skipped': json_results.get('summary', {}).get('skipped', 0)
                        })
                except:
                    pass
                finally:
                    try:
                        os.unlink('temp_results.json')
                    except:
                        pass
            
            # Extract key findings from output
            self.extract_findings(category_name, test_results)
            
            self.results['test_categories'][category_name] = test_results
            
            # Print summary
            print(f"\n📊 {category_name} Results:")
            print(f"   ✅ Passed: {test_results['passed']}")
            print(f"   ❌ Failed: {test_results['failed']}")
            print(f"   ⚠️  Errors: {test_results['errors']}")
            print(f"   ⏭️  Skipped: {test_results['skipped']}")
            print(f"   ⏱️  Time: {execution_time:.1f}s")
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print(f"❌ {category_name} tests timed out after 30 minutes")
            self.results['test_categories'][category_name] = {
                'execution_time': 1800,
                'return_code': -1,
                'error': 'Timeout',
                'passed': 0,
                'failed': 0,
                'errors': 1,
                'skipped': 0
            }
            return False
            
        except Exception as e:
            print(f"❌ Error running {category_name} tests: {e}")
            self.results['test_categories'][category_name] = {
                'execution_time': 0,
                'return_code': -1,
                'error': str(e),
                'passed': 0,
                'failed': 0,
                'errors': 1,
                'skipped': 0
            }
            return False
    
    def extract_findings(self, category_name, test_results):
        """Extract critical findings from test output."""
        output = test_results.get('stdout', '') + test_results.get('stderr', '')
        
        # Look for critical patterns
        critical_patterns = [
            ('Memory leak', ['memory leak', 'leak detected']),
            ('Performance degradation', ['performance degradation', 'too slow', 'timeout']),
            ('Security vulnerability', ['vulnerability', 'attack successful', 'exploit']),
            ('Data corruption', ['corruption', 'invalid data', 'buffer overflow']),
            ('Resource exhaustion', ['exhaustion', 'out of memory', 'resource limit']),
            ('Crash/Segfault', ['segmentation fault', 'segfault', 'crash', 'panic'])
        ]
        
        for finding_type, patterns in critical_patterns:
            for pattern in patterns:
                if pattern.lower() in output.lower():
                    self.results['critical_findings'].append({
                        'category': category_name,
                        'type': finding_type,
                        'pattern': pattern,
                        'severity': 'HIGH' if finding_type in ['Crash/Segfault', 'Security vulnerability'] else 'MEDIUM'
                    })
    
    def generate_summary(self):
        """Generate overall assessment summary."""
        total_tests = sum(cat.get('passed', 0) + cat.get('failed', 0) + cat.get('errors', 0) 
                         for cat in self.results['test_categories'].values())
        total_passed = sum(cat.get('passed', 0) for cat in self.results['test_categories'].values())
        total_failed = sum(cat.get('failed', 0) for cat in self.results['test_categories'].values())
        total_errors = sum(cat.get('errors', 0) for cat in self.results['test_categories'].values())
        
        self.results['summary'] = {
            'total_tests': total_tests,
            'total_passed': total_passed,
            'total_failed': total_failed,
            'total_errors': total_errors,
            'success_rate': (total_passed / total_tests * 100) if total_tests > 0 else 0,
            'critical_findings_count': len(self.results['critical_findings']),
            'categories_tested': len(self.results['test_categories'])
        }
        
        # Generate recommendations based on findings
        self.generate_recommendations()
    
    def generate_recommendations(self):
        """Generate recommendations based on test results."""
        recommendations = []
        
        # Check for high failure rates
        for category, results in self.results['test_categories'].items():
            total = results.get('passed', 0) + results.get('failed', 0) + results.get('errors', 0)
            if total > 0:
                failure_rate = (results.get('failed', 0) + results.get('errors', 0)) / total
                if failure_rate > 0.3:  # 30% failure rate
                    recommendations.append({
                        'priority': 'HIGH',
                        'category': category,
                        'issue': f'High failure rate ({failure_rate:.1%})',
                        'recommendation': f'Review and fix failing tests in {category}'
                    })
        
        # Check for critical findings
        high_severity_findings = [f for f in self.results['critical_findings'] if f['severity'] == 'HIGH']
        if high_severity_findings:
            recommendations.append({
                'priority': 'CRITICAL',
                'category': 'Security',
                'issue': f'{len(high_severity_findings)} high-severity security issues found',
                'recommendation': 'Address all high-severity security vulnerabilities before production deployment'
            })
        
        # Performance recommendations
        performance_issues = [f for f in self.results['critical_findings'] if 'performance' in f['type'].lower()]
        if performance_issues:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': 'Performance',
                'issue': f'{len(performance_issues)} performance issues identified',
                'recommendation': 'Optimize performance bottlenecks and implement better resource management'
            })
        
        # Memory management recommendations
        memory_issues = [f for f in self.results['critical_findings'] if 'memory' in f['type'].lower()]
        if memory_issues:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Memory Management',
                'issue': f'{len(memory_issues)} memory management issues found',
                'recommendation': 'Fix memory leaks and improve memory safety mechanisms'
            })
        
        self.results['recommendations'] = recommendations
    
    def print_final_report(self):
        """Print the final assessment report."""
        print(f"\n{'='*80}")
        print("🎯 CRITICAL ASSESSMENT FINAL REPORT")
        print(f"{'='*80}")
        
        summary = self.results['summary']
        print(f"\n📈 OVERALL RESULTS:")
        print(f"   Total Tests: {summary['total_tests']}")
        print(f"   Passed: {summary['total_passed']} ({summary['success_rate']:.1f}%)")
        print(f"   Failed: {summary['total_failed']}")
        print(f"   Errors: {summary['total_errors']}")
        print(f"   Categories Tested: {summary['categories_tested']}")
        
        print(f"\n🚨 CRITICAL FINDINGS ({len(self.results['critical_findings'])}):")
        if self.results['critical_findings']:
            for finding in self.results['critical_findings']:
                print(f"   [{finding['severity']}] {finding['category']}: {finding['type']}")
        else:
            print("   No critical findings detected ✅")
        
        print(f"\n💡 RECOMMENDATIONS ({len(self.results['recommendations'])}):")
        for rec in self.results['recommendations']:
            print(f"   [{rec['priority']}] {rec['category']}: {rec['recommendation']}")
        
        # Production readiness assessment
        high_critical = len([f for f in self.results['critical_findings'] if f['severity'] == 'HIGH'])
        critical_recs = len([r for r in self.results['recommendations'] if r['priority'] in ['CRITICAL', 'HIGH']])
        
        print(f"\n🏭 PRODUCTION READINESS ASSESSMENT:")
        if high_critical == 0 and critical_recs == 0 and summary['success_rate'] > 90:
            print("   ✅ READY - Extension appears suitable for production deployment")
        elif high_critical == 0 and critical_recs <= 2 and summary['success_rate'] > 80:
            print("   ⚠️  CONDITIONAL - Address recommendations before production deployment")
        else:
            print("   ❌ NOT READY - Critical issues must be resolved before production")
    
    def save_report(self, filename="critical_assessment_report.json"):
        """Save the detailed report to a JSON file."""
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n📄 Detailed report saved to: {filename}")
    
    def run_full_assessment(self):
        """Run the complete critical assessment."""
        print("🚀 Starting Critical Assessment of Streaming JSON Reader Extension")
        print(f"Timestamp: {self.results['timestamp']}")
        
        if not self.check_prerequisites():
            return False
        
        # Run all test categories
        all_passed = True
        for category_name, test_file in self.test_modules:
            success = self.run_test_category(category_name, test_file)
            if not success:
                all_passed = False
        
        # Generate summary and report
        self.generate_summary()
        self.print_final_report()
        self.save_report()
        
        return all_passed


if __name__ == "__main__":
    runner = CriticalAssessmentRunner()
    success = runner.run_full_assessment()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
