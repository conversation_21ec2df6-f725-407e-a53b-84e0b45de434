#!/usr/bin/env python3
"""
Critical Assessment: Memory Management & Safety Tests

This module tests for memory-related vulnerabilities in the streaming JSON reader:
1. Rust memory safety in FFI boundary with DuckDB C API
2. Context preservation overhead with deeply nested structures  
3. Memory leaks in error conditions or partial processing
4. Stack overflow risks with recursive JSON navigation
5. Buffer management during streaming operations
"""

import pytest
import duckdb
import json
import psutil
import os
import tempfile
import gc
import time
from pathlib import Path


class TestMemorySafety:
    """Test suite for memory management and safety vulnerabilities."""
    
    @pytest.fixture(scope="class")
    def duckdb_connection(self):
        """Create a DuckDB connection with the streaming JSON reader extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        # Load the extension
        extension_path = "../build/debug/streaming_json_reader.duckdb_extension"
        if not Path(extension_path).exists():
            pytest.skip(f"Extension not found at {extension_path}. Run 'make debug' first.")

        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    def get_memory_usage(self):
        """Get current memory usage in MB."""
        process = psutil.Process(os.getpid())
        return process.memory_info().rss / (1024 * 1024)
    
    def create_deeply_nested_json(self, depth=100):
        """Create JSON with extremely deep nesting to test stack limits."""
        data = "data"
        for i in range(depth):
            data = {f"level{depth-i}": data}
        
        # Wrap in users array structure expected by extension
        return {"users": [data]}
    
    def create_wide_object_json(self, field_count=10000):
        """Create JSON with very wide objects to test memory allocation."""
        user = {}
        for i in range(field_count):
            user[f"field{i}"] = f"value{i}"
        
        return {"users": [user]}
    
    def create_mixed_size_objects_json(self, count=1000):
        """Create JSON with mixed large and small objects to test memory fragmentation."""
        users = []
        for i in range(count):
            if i % 3 == 0:
                # Large object
                user = {
                    "id": i,
                    "type": "large",
                    "data": "A" * 10000,  # 10KB string
                    "bio": "B" * 50000    # 50KB string
                }
            else:
                # Small object
                user = {
                    "id": i,
                    "type": "small",
                    "name": f"User{i}"
                }
            users.append(user)
        
        return {"users": users}
    
    def test_extremely_deep_nesting_stack_safety(self, duckdb_connection):
        """Test stack overflow protection with extremely deep nesting."""
        print("\n=== Testing Stack Safety with Deep Nesting ===")
        
        # Test various nesting depths
        for depth in [50, 100, 200, 500, 1000]:
            print(f"Testing depth: {depth}")
            
            try:
                nested_data = self.create_deeply_nested_json(depth)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(nested_data, f)
                    temp_file = f.name
                
                try:
                    # Measure memory before
                    mem_before = self.get_memory_usage()
                    
                    # This should either work or fail gracefully, not crash
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    mem_after = self.get_memory_usage()
                    mem_diff = mem_after - mem_before
                    
                    print(f"  Depth {depth}: SUCCESS - Memory used: {mem_diff:.1f}MB")
                    
                    # Memory usage should be reasonable (not proportional to depth)
                    assert mem_diff < 100, f"Memory usage too high: {mem_diff}MB for depth {depth}"
                    
                except Exception as e:
                    print(f"  Depth {depth}: FAILED - {str(e)[:100]}...")
                    # Failure is acceptable, but should be graceful
                    assert "stack overflow" not in str(e).lower(), "Stack overflow detected!"
                
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  Depth {depth}: SETUP FAILED - {str(e)[:100]}...")
                # Even setup failures should be graceful
                continue
    
    def test_wide_object_memory_allocation(self, duckdb_connection):
        """Test memory allocation with very wide objects."""
        print("\n=== Testing Wide Object Memory Allocation ===")
        
        # Test various field counts
        for field_count in [1000, 5000, 10000, 20000]:
            print(f"Testing field count: {field_count}")
            
            try:
                wide_data = self.create_wide_object_json(field_count)
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(wide_data, f)
                    temp_file = f.name
                
                try:
                    mem_before = self.get_memory_usage()
                    
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    mem_after = self.get_memory_usage()
                    mem_diff = mem_after - mem_before
                    
                    print(f"  Fields {field_count}: SUCCESS - Memory used: {mem_diff:.1f}MB")
                    
                    # Memory should not grow excessively with field count
                    max_expected_mb = field_count * 0.001  # Very rough estimate
                    assert mem_diff < max_expected_mb + 50, f"Memory usage too high: {mem_diff}MB"
                    
                except Exception as e:
                    print(f"  Fields {field_count}: FAILED - {str(e)[:100]}...")
                    # Check for memory-related errors
                    error_str = str(e).lower()
                    assert "out of memory" not in error_str, "Out of memory error detected!"
                    assert "allocation" not in error_str, "Memory allocation error detected!"
                
                finally:
                    os.unlink(temp_file)
                    
            except Exception as e:
                print(f"  Fields {field_count}: SETUP FAILED - {str(e)[:100]}...")
                continue
    
    def test_memory_fragmentation_patterns(self, duckdb_connection):
        """Test memory fragmentation with mixed large and small objects."""
        print("\n=== Testing Memory Fragmentation Patterns ===")
        
        try:
            mixed_data = self.create_mixed_size_objects_json(1000)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(mixed_data, f)
                temp_file = f.name
            
            try:
                # Force garbage collection before test
                gc.collect()
                mem_before = self.get_memory_usage()
                
                # Process the mixed-size data multiple times to test fragmentation
                for iteration in range(3):
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                    ).fetchone()
                    
                    mem_current = self.get_memory_usage()
                    print(f"  Iteration {iteration + 1}: Memory: {mem_current:.1f}MB")
                
                mem_after = self.get_memory_usage()
                mem_diff = mem_after - mem_before
                
                print(f"Total memory increase: {mem_diff:.1f}MB")
                
                # Memory should not grow significantly between iterations (no major leaks)
                assert mem_diff < 200, f"Potential memory leak detected: {mem_diff}MB increase"
                
            finally:
                os.unlink(temp_file)
                
        except Exception as e:
            print(f"Memory fragmentation test failed: {e}")
            pytest.fail(f"Memory fragmentation test failed: {e}")
    
    def test_error_condition_memory_leaks(self, duckdb_connection):
        """Test for memory leaks in error conditions."""
        print("\n=== Testing Error Condition Memory Leaks ===")
        
        # Create various malformed JSON files
        malformed_cases = [
            '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "invalid":',  # Truncated
            '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}',  # Missing closing
            '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": null}]}',  # Null value
            '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": }]}',  # Empty value
        ]
        
        gc.collect()
        mem_before = self.get_memory_usage()
        
        for i, malformed_json in enumerate(malformed_cases):
            print(f"Testing malformed case {i + 1}")
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                f.write(malformed_json)
                temp_file = f.name
            
            try:
                # This should fail gracefully without leaking memory
                result = duckdb_connection.execute(
                    f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                ).fetchall()
                
            except Exception as e:
                # Errors are expected for malformed JSON
                print(f"  Expected error: {str(e)[:50]}...")
                
            finally:
                os.unlink(temp_file)
        
        gc.collect()
        mem_after = self.get_memory_usage()
        mem_diff = mem_after - mem_before
        
        print(f"Memory change after error tests: {mem_diff:.1f}MB")
        
        # Should not have significant memory leaks from error handling
        assert abs(mem_diff) < 50, f"Potential memory leak in error handling: {mem_diff}MB"
    
    def test_concurrent_access_memory_safety(self, duckdb_connection):
        """Test memory safety under concurrent access patterns."""
        print("\n=== Testing Concurrent Access Memory Safety ===")
        
        # Create a test file
        test_data = {"users": [{"id": i, "name": f"User{i}"} for i in range(100)]}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            mem_before = self.get_memory_usage()
            
            # Simulate concurrent access by rapid successive queries
            for i in range(10):
                result = duckdb_connection.execute(
                    f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                ).fetchone()
                
                # Small delay to allow for cleanup
                time.sleep(0.1)
            
            mem_after = self.get_memory_usage()
            mem_diff = mem_after - mem_before
            
            print(f"Memory change after concurrent access: {mem_diff:.1f}MB")
            
            # Memory should not accumulate significantly
            assert mem_diff < 100, f"Memory accumulation in concurrent access: {mem_diff}MB"
            
        finally:
            os.unlink(temp_file)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
