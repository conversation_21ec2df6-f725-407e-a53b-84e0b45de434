{"timestamp": "2025-06-29T21:19:20.187046", "test_categories": {"Memory Safety": {"execution_time": 0.10088706016540527, "return_code": 4, "stdout": "", "stderr": "ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=temp_results.json\n  inifile: None\n  rootdir: /Users/<USER>/projects/duckdb-json-extension/extension-template-rs/critical_assessment\n\n", "passed": 0, "failed": 0, "errors": 0, "skipped": 0}, "Performance Characteristics": {"execution_time": 0.09314465522766113, "return_code": 4, "stdout": "", "stderr": "ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=temp_results.json\n  inifile: None\n  rootdir: /Users/<USER>/projects/duckdb-json-extension/extension-template-rs/critical_assessment\n\n", "passed": 0, "failed": 0, "errors": 0, "skipped": 0}, "Edge Cases & Error Handling": {"execution_time": 0.09185528755187988, "return_code": 4, "stdout": "", "stderr": "ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=temp_results.json\n  inifile: None\n  rootdir: /Users/<USER>/projects/duckdb-json-extension/extension-template-rs/critical_assessment\n\n", "passed": 0, "failed": 0, "errors": 0, "skipped": 0}, "Security Vulnerabilities": {"execution_time": 0.09279894828796387, "return_code": 4, "stdout": "", "stderr": "ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=temp_results.json\n  inifile: None\n  rootdir: /Users/<USER>/projects/duckdb-json-extension/extension-template-rs/critical_assessment\n\n", "passed": 0, "failed": 0, "errors": 0, "skipped": 0}, "Scalability & Resource Limits": {"execution_time": 0.09221005439758301, "return_code": 4, "stdout": "", "stderr": "ERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=temp_results.json\n  inifile: None\n  rootdir: /Users/<USER>/projects/duckdb-json-extension/extension-template-rs/critical_assessment\n\n", "passed": 0, "failed": 0, "errors": 0, "skipped": 0}}, "summary": {"total_tests": 0, "total_passed": 0, "total_failed": 0, "total_errors": 0, "success_rate": 0, "critical_findings_count": 0, "categories_tested": 5}, "critical_findings": [], "recommendations": []}