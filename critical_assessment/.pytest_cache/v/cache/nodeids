["edge_case_tests.py::TestEdgeCasesAndErrorHandling::test_empty_and_null_scenarios", "edge_case_tests.py::TestEdgeCasesAndErrorHandling::test_file_io_errors", "edge_case_tests.py::TestEdgeCasesAndErrorHandling::test_malformed_json_handling", "edge_case_tests.py::TestEdgeCasesAndErrorHandling::test_schema_inconsistencies", "edge_case_tests.py::TestEdgeCasesAndErrorHandling::test_unicode_and_special_characters", "memory_safety_tests.py::TestMemorySafety::test_concurrent_access_memory_safety", "memory_safety_tests.py::TestMemorySafety::test_error_condition_memory_leaks", "memory_safety_tests.py::TestMemorySafety::test_extremely_deep_nesting_stack_safety", "memory_safety_tests.py::TestMemorySafety::test_memory_fragmentation_patterns", "memory_safety_tests.py::TestMemorySafety::test_wide_object_memory_allocation", "performance_tests.py::TestPerformanceCharacteristics::test_context_replication_overhead", "performance_tests.py::TestPerformanceCharacteristics::test_cpu_vs_memory_tradeoffs", "performance_tests.py::TestPerformanceCharacteristics::test_large_string_performance", "performance_tests.py::TestPerformanceCharacteristics::test_memory_allocation_patterns", "performance_tests.py::TestPerformanceCharacteristics::test_pathological_nesting_performance", "security_tests.py::TestSecurityVulnerabilities::test_data_corruption_attacks", "security_tests.py::TestSecurityVulnerabilities::test_memory_exhaustion_attacks"]