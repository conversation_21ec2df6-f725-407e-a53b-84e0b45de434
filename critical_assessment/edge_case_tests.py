#!/usr/bin/env python3
"""
Critical Assessment: Edge Cases & Error Handling Tests

This module tests for edge cases and error handling in the streaming JSON reader:
1. Malformed JSON handling and error propagation
2. Unicode and special character handling
3. Empty arrays, null values, missing fields
4. Inconsistent schema across array elements
5. File I/O errors and partial reads
"""

import pytest
import duckdb
import json
import os
import tempfile
import stat
from pathlib import Path


class TestEdgeCasesAndErrorHandling:
    """Test suite for edge cases and error handling."""
    
    @pytest.fixture(scope="class")
    def duckdb_connection(self):
        """Create a DuckDB connection with the streaming JSON reader extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        # Load the extension
        extension_path = "../build/debug/streaming_json_reader.duckdb_extension"
        if not Path(extension_path).exists():
            pytest.skip(f"Extension not found at {extension_path}. Run 'make debug' first.")

        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    def create_malformed_json_files(self):
        """Create various malformed JSON files for testing."""
        malformed_cases = {
            "truncated_object": '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name":',
            "missing_closing_bracket": '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}',
            "invalid_syntax": '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": }]}',
            "extra_comma": '{"users": [{"id": 1, "name": "Alice",}, {"id": 2, "name": "Bob"}]}',
            "unquoted_keys": '{users: [{"id": 1, "name": "Alice"}]}',
            "single_quotes": "{'users': [{'id': 1, 'name': 'Alice'}]}",
            "trailing_comma": '{"users": [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"},]}',
            "invalid_escape": '{"users": [{"id": 1, "name": "Alice\\x"}]}',
            "incomplete_string": '{"users": [{"id": 1, "name": "Alice}]}',
            "invalid_number": '{"users": [{"id": 1.2.3, "name": "Alice"}]}',
            "mixed_quotes": '{"users": [{"id": 1, "name": \'Alice"}]}',
            "control_chars": '{"users": [{"id": 1, "name": "Alice\x00Bob"}]}',
        }
        
        files = {}
        for case_name, content in malformed_cases.items():
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                f.write(content)
                files[case_name] = f.name
        
        return files
    
    def create_unicode_test_files(self):
        """Create JSON files with various Unicode and special characters."""
        unicode_cases = {
            "basic_unicode": {
                "users": [
                    {"id": 1, "name": "José", "city": "São Paulo"},
                    {"id": 2, "name": "北京", "city": "中国"},
                    {"id": 3, "name": "Москва", "city": "Россия"}
                ]
            },
            "emoji_data": {
                "users": [
                    {"id": 1, "name": "🚀💻", "bio": "Developer with 🔥 skills"},
                    {"id": 2, "name": "Alice 👩‍💻", "status": "Working from 🏠"}
                ]
            },
            "special_chars": {
                "users": [
                    {"id": 1, "name": "Alice", "bio": "Line1\nLine2\tTabbed"},
                    {"id": 2, "name": "Bob", "data": "Quote: \"Hello\" and 'World'"},
                    {"id": 3, "name": "Charlie", "path": "C:\\Users\\<USER>\\Documents"}
                ]
            },
            "unicode_escapes": {
                "users": [
                    {"id": 1, "name": "\u0041\u006C\u0069\u0063\u0065"},  # "Alice" in Unicode escapes
                    {"id": 2, "name": "\u4E2D\u6587"},  # Chinese characters
                    {"id": 3, "data": "\uD83D\uDE80\uD83D\uDCBB"}  # Rocket and laptop emojis
                ]
            },
            "null_and_control": {
                "users": [
                    {"id": 1, "name": "Alice", "data": "Before\u0000After"},  # Null character
                    {"id": 2, "name": "Bob", "data": "Bell\u0007Sound"},  # Bell character
                    {"id": 3, "name": "Charlie", "data": "Form\u000CFeed"}  # Form feed
                ]
            }
        }
        
        files = {}
        for case_name, data in unicode_cases.items():
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False)
                files[case_name] = f.name
        
        return files
    
    def create_schema_inconsistency_files(self):
        """Create JSON files with inconsistent schemas."""
        schema_cases = {
            "mixed_types": {
                "users": [
                    {"id": 1, "name": "Alice", "age": 30, "active": True},
                    {"id": "2", "name": "Bob", "age": "25", "active": "yes"},
                    {"id": 3.0, "name": "Charlie", "age": None, "active": 1}
                ]
            },
            "missing_fields": {
                "users": [
                    {"id": 1, "name": "Alice", "email": "<EMAIL>"},
                    {"id": 2, "name": "Bob"},  # Missing email
                    {"id": 3, "email": "<EMAIL>"},  # Missing name
                    {"name": "David", "email": "<EMAIL>"}  # Missing id
                ]
            },
            "extra_fields": {
                "users": [
                    {"id": 1, "name": "Alice"},
                    {"id": 2, "name": "Bob", "extra_field": "unexpected"},
                    {"id": 3, "name": "Charlie", "metadata": {"created": "2024-01-01", "source": "import"}}
                ]
            },
            "nested_inconsistency": {
                "users": [
                    {"id": 1, "name": "Alice", "projects": [{"name": "P1", "budget": 1000}]},
                    {"id": 2, "name": "Bob", "projects": "No projects"},  # String instead of array
                    {"id": 3, "name": "Charlie", "projects": [{"title": "P3", "cost": 2000}]},  # Different field names
                    {"id": 4, "name": "David", "projects": None}  # Null instead of array
                ]
            },
            "array_vs_object": {
                "users": [
                    {"id": 1, "name": "Alice", "tags": ["developer", "python"]},
                    {"id": 2, "name": "Bob", "tags": {"primary": "manager", "secondary": "leader"}},
                    {"id": 3, "name": "Charlie", "tags": "single_tag"}
                ]
            }
        }
        
        files = {}
        for case_name, data in schema_cases.items():
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(data, f)
                files[case_name] = f.name
        
        return files
    
    def create_empty_and_null_files(self):
        """Create JSON files with empty and null scenarios."""
        empty_cases = {
            "empty_users_array": {"users": []},
            "empty_object": {},
            "null_users": {"users": None},
            "empty_string": "",
            "only_whitespace": "   \n\t  ",
            "null_values": {
                "users": [
                    {"id": 1, "name": None, "email": None},
                    {"id": None, "name": "Bob", "email": "<EMAIL>"}
                ]
            },
            "empty_strings": {
                "users": [
                    {"id": 1, "name": "", "email": ""},
                    {"id": 2, "name": "Bob", "email": ""}
                ]
            },
            "mixed_empty": {
                "users": [
                    {"id": 1, "name": "Alice", "projects": []},
                    {"id": 2, "name": "", "projects": None},
                    {"id": 3, "name": None, "projects": []}
                ]
            }
        }
        
        files = {}
        for case_name, data in empty_cases.items():
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                if isinstance(data, str):
                    f.write(data)
                else:
                    json.dump(data, f)
                files[case_name] = f.name
        
        return files
    
    def test_malformed_json_handling(self, duckdb_connection):
        """Test handling of various malformed JSON files."""
        print("\n=== Testing Malformed JSON Handling ===")
        
        malformed_files = self.create_malformed_json_files()
        
        try:
            for case_name, file_path in malformed_files.items():
                print(f"Testing {case_name}")
                
                try:
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{file_path}')"
                    ).fetchall()
                    
                    print(f"  {case_name}: Unexpected success - {result}")
                    # Some malformed JSON might be handled gracefully
                    
                except Exception as e:
                    print(f"  {case_name}: Expected error - {str(e)[:100]}...")
                    # Errors should be informative, not crashes
                    error_str = str(e).lower()
                    assert "segmentation fault" not in error_str, "Segmentation fault detected!"
                    assert "panic" not in error_str, "Rust panic detected!"
                    
        finally:
            # Cleanup
            for file_path in malformed_files.values():
                try:
                    os.unlink(file_path)
                except OSError:
                    pass
    
    def test_unicode_and_special_characters(self, duckdb_connection):
        """Test handling of Unicode and special characters."""
        print("\n=== Testing Unicode and Special Characters ===")
        
        unicode_files = self.create_unicode_test_files()
        
        try:
            for case_name, file_path in unicode_files.items():
                print(f"Testing {case_name}")
                
                try:
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{file_path}')"
                    ).fetchone()
                    
                    print(f"  {case_name}: SUCCESS - {result[0]} rows")
                    assert result[0] >= 0, "Should return non-negative row count"
                    
                    # Test that we can actually retrieve the data
                    data_result = duckdb_connection.execute(
                        f"SELECT * FROM streaming_json_reader('{file_path}') LIMIT 1"
                    ).fetchall()
                    
                    print(f"  {case_name}: Data retrieval successful")
                    
                except Exception as e:
                    print(f"  {case_name}: FAILED - {str(e)[:100]}...")
                    # Unicode should generally be supported
                    if case_name in ["basic_unicode", "emoji_data"]:
                        pytest.fail(f"Basic Unicode support failed for {case_name}: {e}")
                    
        finally:
            # Cleanup
            for file_path in unicode_files.values():
                try:
                    os.unlink(file_path)
                except OSError:
                    pass
    
    def test_schema_inconsistencies(self, duckdb_connection):
        """Test handling of inconsistent schemas across array elements."""
        print("\n=== Testing Schema Inconsistencies ===")
        
        schema_files = self.create_schema_inconsistency_files()
        
        try:
            for case_name, file_path in schema_files.items():
                print(f"Testing {case_name}")
                
                try:
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{file_path}')"
                    ).fetchone()
                    
                    print(f"  {case_name}: SUCCESS - {result[0]} rows")
                    
                    # Test data retrieval with schema inconsistencies
                    data_result = duckdb_connection.execute(
                        f"SELECT * FROM streaming_json_reader('{file_path}')"
                    ).fetchall()
                    
                    print(f"  {case_name}: Retrieved {len(data_result)} rows")
                    
                except Exception as e:
                    print(f"  {case_name}: FAILED - {str(e)[:100]}...")
                    # Schema inconsistencies should be handled gracefully
                    error_str = str(e).lower()
                    assert "type mismatch" not in error_str or "graceful" in error_str, "Type mismatches should be handled gracefully"
                    
        finally:
            # Cleanup
            for file_path in schema_files.values():
                try:
                    os.unlink(file_path)
                except OSError:
                    pass
    
    def test_empty_and_null_scenarios(self, duckdb_connection):
        """Test handling of empty arrays, null values, and missing fields."""
        print("\n=== Testing Empty and Null Scenarios ===")
        
        empty_files = self.create_empty_and_null_files()
        
        try:
            for case_name, file_path in empty_files.items():
                print(f"Testing {case_name}")
                
                try:
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{file_path}')"
                    ).fetchone()
                    
                    print(f"  {case_name}: SUCCESS - {result[0]} rows")
                    
                    # Empty scenarios should return 0 rows, not errors
                    if case_name in ["empty_users_array", "empty_object", "empty_string"]:
                        assert result[0] == 0, f"Empty scenarios should return 0 rows, got {result[0]}"
                    
                except Exception as e:
                    print(f"  {case_name}: FAILED - {str(e)[:100]}...")
                    # Empty files should generally be handled gracefully
                    if case_name in ["empty_users_array", "empty_object"]:
                        pytest.fail(f"Empty scenarios should be handled gracefully: {e}")
                    
        finally:
            # Cleanup
            for file_path in empty_files.values():
                try:
                    os.unlink(file_path)
                except OSError:
                    pass
    
    def test_file_io_errors(self, duckdb_connection):
        """Test handling of file I/O errors and edge cases."""
        print("\n=== Testing File I/O Errors ===")
        
        # Test non-existent file
        try:
            result = duckdb_connection.execute(
                "SELECT COUNT(*) FROM streaming_json_reader('/nonexistent/path/file.json')"
            ).fetchall()
            print("Non-existent file: Handled gracefully")
        except Exception as e:
            print(f"Non-existent file: Error (expected) - {str(e)[:100]}...")
            assert "no such file" in str(e).lower() or "not found" in str(e).lower(), "Should indicate file not found"
        
        # Test directory instead of file
        try:
            result = duckdb_connection.execute(
                f"SELECT COUNT(*) FROM streaming_json_reader('.')"
            ).fetchall()
            print("Directory as file: Handled gracefully")
        except Exception as e:
            print(f"Directory as file: Error (expected) - {str(e)[:100]}...")
        
        # Test file with no read permissions
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump({"users": [{"id": 1, "name": "Alice"}]}, f)
            no_read_file = f.name
        
        try:
            # Remove read permissions
            os.chmod(no_read_file, stat.S_IWRITE)
            
            result = duckdb_connection.execute(
                f"SELECT COUNT(*) FROM streaming_json_reader('{no_read_file}')"
            ).fetchall()
            print("No read permissions: Handled gracefully")
            
        except Exception as e:
            print(f"No read permissions: Error (expected) - {str(e)[:100]}...")
            assert "permission" in str(e).lower() or "access" in str(e).lower(), "Should indicate permission error"
            
        finally:
            try:
                os.chmod(no_read_file, stat.S_IREAD | stat.S_IWRITE)
                os.unlink(no_read_file)
            except OSError:
                pass


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
