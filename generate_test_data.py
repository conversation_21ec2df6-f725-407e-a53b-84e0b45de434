#!/usr/bin/env python3
"""
Generate large JSON test files to demonstrate memory efficiency of streaming JSON reader.
"""

import json
import random
import string
from pathlib import Path

def generate_random_string(length=10):
    """Generate a random string of specified length."""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

def generate_user_object(user_id):
    """Generate a single user object with realistic data."""
    return {
        "id": user_id,
        "name": f"User_{user_id}",
        "email": f"user{user_id}@example.com",
        "age": random.randint(18, 80),
        "department": random.choice(["Engineering", "Sales", "Marketing", "HR", "Finance"]),
        "salary": random.randint(30000, 150000),
        "skills": [generate_random_string(8) for _ in range(random.randint(2, 8))],
        "bio": generate_random_string(100),  # Long text field
        "metadata": {
            "created_at": f"2024-{random.randint(1,12):02d}-{random.randint(1,28):02d}",
            "last_login": f"2025-{random.randint(1,6):02d}-{random.randint(1,29):02d}",
            "preferences": {
                "theme": random.choice(["dark", "light"]),
                "notifications": random.choice([True, False]),
                "language": random.choice(["en", "es", "fr", "de"])
            }
        },
        "projects": [
            {
                "name": f"Project_{generate_random_string(5)}",
                "status": random.choice(["active", "completed", "on_hold"]),
                "priority": random.choice(["low", "medium", "high"]),
                "budget": random.randint(1000, 100000),
                "description": generate_random_string(50)
            }
            for _ in range(random.randint(1, 5))
        ]
    }

def generate_large_json_file(filename, num_users, include_metadata=True):
    """Generate a large JSON file with specified number of users."""
    print(f"Generating {filename} with {num_users} users...")
    
    data = {
        "dataset_info": {
            "name": "Large User Dataset",
            "version": "1.0",
            "created": "2025-06-29",
            "total_users": num_users,
            "description": "Large dataset for testing memory-efficient JSON processing"
        } if include_metadata else {},
        "users": [generate_user_object(i) for i in range(1, num_users + 1)]
    }
    
    with open(filename, 'w') as f:
        json.dump(data, f, separators=(',', ':'))  # Compact format
    
    file_size = Path(filename).stat().st_size
    print(f"Generated {filename}: {file_size / (1024*1024):.1f} MB")

def generate_deeply_nested_json(filename, depth_levels=3, items_per_level=10):
    """Generate JSON with deeply nested arrays for testing nested path support."""
    print(f"Generating {filename} with {depth_levels} levels of nesting...")
    
    def create_nested_structure(level, max_level, item_id_base=0):
        if level >= max_level:
            # Leaf level - return simple objects
            return [
                {
                    "id": item_id_base + i,
                    "name": f"Item_{item_id_base + i}",
                    "value": random.randint(1, 1000),
                    "data": generate_random_string(20)
                }
                for i in range(items_per_level)
            ]
        else:
            # Intermediate level - create nested structure
            return [
                {
                    "id": item_id_base + i,
                    "name": f"Level_{level}_Item_{i}",
                    "category": f"Category_{random.randint(1, 5)}",
                    "children": create_nested_structure(
                        level + 1, 
                        max_level, 
                        (item_id_base + i) * items_per_level
                    )
                }
                for i in range(items_per_level)
            ]
    
    data = {
        "metadata": {
            "name": "Deeply Nested Dataset",
            "levels": depth_levels,
            "items_per_level": items_per_level
        },
        "root_items": create_nested_structure(0, depth_levels)
    }
    
    with open(filename, 'w') as f:
        json.dump(data, f, separators=(',', ':'))
    
    file_size = Path(filename).stat().st_size
    print(f"Generated {filename}: {file_size / (1024*1024):.1f} MB")

if __name__ == "__main__":
    # Generate test files of various sizes
    
    # Small file for basic testing (should work with both readers)
    generate_large_json_file("test_small.json", 100)
    
    # Medium file (might stress default reader)
    generate_large_json_file("test_medium.json", 10000)
    
    # Large file (likely to cause memory issues with default reader)
    generate_large_json_file("test_large.json", 100000)
    
    # Very large file (definitely will cause OOM with default reader)
    # generate_large_json_file("test_very_large.json", 1000000)  # Uncomment for extreme testing
    
    # Deeply nested structure
    generate_deeply_nested_json("test_nested_deep.json", depth_levels=4, items_per_level=20)
    
    print("\nTest files generated successfully!")
    print("Use these files to compare memory usage between default DuckDB JSON reader and streaming reader.")
