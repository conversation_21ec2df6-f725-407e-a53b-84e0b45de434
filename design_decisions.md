# Design Decisions and Functionality Approaches

This document captures all major design decisions and functionality approaches made during the development of the Streaming JSON Reader Extension for DuckDB.

## 1. Core Architecture Decisions

### 1.1 Streaming vs Traditional JSON Processing
**Decision**: Use streaming JSON parsing with the `struson` crate instead of loading entire JSON objects into memory.

**Rationale**:
- Traditional approach: O(file_size) memory usage
- Streaming approach: O(row_size) memory usage
- Enables processing of files larger than available RAM
- Prevents out-of-memory failures on large datasets

**Implementation**: 
```rust
let mut json_reader = JsonStreamReader::new(buf_reader);
while json_reader.has_next()? {
    // Process tokens incrementally without building full object tree
}
```

### 1.2 Language Choice: Rust
**Decision**: Implement the extension in Rust using DuckDB's C API bindings.

**Rationale**:
- Memory safety guarantees crucial for database extensions
- Performance characteristics suitable for streaming operations
- Strong ecosystem for JSON parsing (struson crate)
- FFI capabilities for DuckDB integration
- User preference for Rust development

**Trade-offs**:
- More complex FFI boundary management
- Steeper learning curve for DuckDB extension development
- Limited documentation compared to C++ extensions

## 2. JSON Path Navigation Strategy

### 2.1 Automatic Path Inference
**Decision**: Implement automatic JSON path inference based on query column patterns rather than requiring explicit path parameters.

**Rationale**:
- Reduces user configuration burden
- Enables "just works" experience: `SELECT * FROM streaming_json_reader('file.json')`
- Leverages DuckDB's projection pushdown capabilities
- More intuitive API design

**Implementation Approach**:
```rust
fn infer_optimal_path(projected_columns: &[String]) -> String {
    // Analyze column patterns to determine best JSON path
    // e.g., ["user_name", "project_name"] → "users.projects"
}
```

### 2.2 Context Preservation for Nested Structures
**Decision**: Maintain parent object context when flattening nested arrays (e.g., user info in project rows).

**Rationale**:
- Eliminates need for complex JOINs in queries
- Provides complete information in flattened result set
- Matches user expectations for nested data processing

**Memory Impact**: O(parent_fields × child_count) per batch
**Performance Trade-off**: Increased row count vs eliminated JOIN operations

## 3. Memory Management Strategies

### 3.1 Context Replication Strategy
**Decision**: Store parent context in local variables and replicate in each child row rather than using heap allocation.

**Rationale**:
- Minimizes heap allocations during streaming
- Predictable memory usage patterns
- Avoids complex reference management across FFI boundary

**Example**:
```rust
// Capture user context
let mut user_id = String::new();
let mut user_name = String::new();

// Replicate in each project row
let row_data = vec![
    user_id.clone(),
    user_name.clone(), 
    project_name,
    project_status,
];
```

### 3.2 Fixed Schema Approach
**Decision**: Define all possible columns at bind time rather than dynamic schema discovery.

**Rationale**:
- Simpler implementation with DuckDB's table function API
- Predictable memory allocation patterns
- Avoids complex schema evolution handling

**Limitation**: Results in empty columns for unused fields
**Future Enhancement**: Dynamic schema discovery based on JSON structure

## 4. Error Handling Philosophy

### 4.1 Fail-Fast Error Propagation
**Decision**: Stop processing and return errors immediately on malformed JSON rather than attempting partial recovery.

**Rationale**:
- Maintains transactional semantics
- Prevents silent data corruption
- Simpler error handling logic
- Clear failure modes for debugging

**Trade-off**: No partial results on parse errors vs data integrity guarantees

### 4.2 Memory Safety in FFI Boundary
**Decision**: Avoid storing Rust String types in `#[repr(C)]` structs passed to DuckDB.

**Rationale**:
- Prevents segmentation faults from memory layout mismatches
- Ensures proper cleanup of Rust-managed memory
- Maintains safety across language boundaries

**Implementation**: Use local variables and explicit cloning instead of struct fields

## 5. Performance Optimization Decisions

### 5.1 Batch Processing Limits
**Decision**: Implement 100-element limits per array level to prevent memory exhaustion.

**Rationale**:
- Prevents pathological cases with extremely large arrays
- Maintains predictable memory usage
- Balances throughput with resource constraints

**Configuration**: Hardcoded limit, future enhancement for user configuration

### 5.2 String-Only Type System
**Decision**: Convert all JSON values to VARCHAR rather than preserving native types.

**Rationale**:
- Simpler implementation with DuckDB's type system
- Avoids complex type inference and conversion logic
- Consistent handling across different JSON value types

**Limitation**: Type information lost, requires explicit casting in queries
**Future Enhancement**: Native type preservation

## 6. API Design Principles

### 6.1 Minimal Configuration Philosophy
**Decision**: Require only file path parameter, infer everything else automatically.

**Rationale**:
- Reduces cognitive load for users
- Enables exploratory data analysis workflows
- Matches user preference for simple, minimal scripts

**API**: `SELECT * FROM streaming_json_reader('file.json')`

### 6.2 SQL-First Integration
**Decision**: Integrate as a table function rather than a separate tool or CLI utility.

**Rationale**:
- Leverages existing SQL knowledge and tooling
- Enables composition with other DuckDB features
- Provides familiar query interface for data analysis

## 7. Testing Strategy Decisions

### 7.1 Pytest Structure for Reusability
**Decision**: Implement comprehensive pytest test suite with reusable fixtures and test data generation.

**Rationale**:
- User preference for pytest structure
- Enables automated regression testing
- Provides foundation for continuous integration
- Facilitates debugging and development iteration

**Implementation**: 9 test cases covering functionality, memory efficiency, and edge cases

### 7.2 Memory Efficiency Validation
**Decision**: Include comparative memory testing against DuckDB's default JSON reader.

**Rationale**:
- Validates core value proposition of the extension
- Provides concrete performance metrics
- Enables regression detection for memory usage

## 8. Incremental Development Approach

### 8.1 Feature Progression Strategy
**Decision**: Build functionality incrementally: basic reading → array flattening → nested paths → path inference.

**Rationale**:
- User preference for incremental git commits
- Enables early validation of core concepts
- Reduces risk of complex integration issues
- Facilitates debugging and testing

**Progression**:
1. Basic JSON file reading
2. Array flattening functionality  
3. Multiply-nested path support
4. Automatic path inference
5. Comprehensive testing

### 8.2 Commit Strategy
**Decision**: Make detailed, incremental commits with comprehensive descriptions.

**Rationale**:
- User preference for incremental git commits
- Facilitates code review and debugging
- Provides clear development history
- Enables selective rollback if needed

## 9. Future Architecture Considerations

### 9.1 Projection Pushdown Integration
**Decision**: Implement framework for projection pushdown but acknowledge limitations with current DuckDB Rust bindings.

**Rationale**:
- Prepares for future API improvements
- Demonstrates understanding of optimization opportunities
- Provides foundation for enhanced performance

**Current Status**: Framework exists, full integration pending API availability

### 9.2 Extensibility Design
**Decision**: Design with future enhancements in mind (configurable paths, type preservation, parallel processing).

**Rationale**:
- Anticipates user needs for production deployment
- Provides clear upgrade path
- Maintains architectural flexibility

## 10. Documentation Philosophy

### 10.1 Comprehensive Technical Documentation
**Decision**: Create detailed README with performance characteristics, edge cases, and limitations.

**Rationale**:
- User request for detailed documentation
- Enables informed deployment decisions
- Facilitates maintenance and debugging
- Provides foundation for external assessment

### 10.2 Critical Assessment Framework
**Decision**: Provide structured methodology for external evaluation and testing.

**Rationale**:
- Acknowledges limitations and areas for improvement
- Enables thorough security and performance evaluation
- Demonstrates commitment to production readiness
- Facilitates community contribution and feedback

---

**Key Principles Throughout Development**:
- Memory efficiency as primary goal
- User experience simplicity
- Incremental, testable development
- Honest assessment of limitations
- Future-oriented architecture design
