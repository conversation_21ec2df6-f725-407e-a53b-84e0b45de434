extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{JsonReader, JsonStreamReader};

// Custom error types for better error handling
#[derive(Debug)]
pub enum JsonReaderError {
    ParseError(String),
    IoError(std::io::Error),
    InvalidStructure(String),
    StateError(String),
}

impl std::fmt::Display for JsonReaderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            JsonReaderError::ParseError(msg) => write!(f, "JSON parse error: {}", msg),
            JsonReaderError::IoError(err) => write!(f, "File I/O error: {}", err),
            JsonReaderError::InvalidStructure(msg) => write!(f, "Invalid JSON structure: {}", msg),
            JsonReaderError::StateError(msg) => write!(f, "State error: {}", msg),
        }
    }
}

impl std::error::Error for JsonReaderError {}

impl From<std::io::Error> for JsonReaderError {
    fn from(err: std::io::Error) -> Self {
        JsonReaderError::IoError(err)
    }
}

impl From<struson::reader::ReaderError> for JsonReaderError {
    fn from(err: struson::reader::ReaderError) -> Self {
        JsonReaderError::ParseError(err.to_string())
    }
}

impl From<&str> for JsonReaderError {
    fn from(err: &str) -> Self {
        JsonReaderError::InvalidStructure(err.to_string())
    }
}

impl From<String> for JsonReaderError {
    fn from(err: String) -> Self {
        JsonReaderError::InvalidStructure(err)
    }
}



#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    json_path: Option<String>,
    columns: Vec<String>,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    // Remove the String field to avoid memory safety issues in repr(C) struct
}

struct JsonReaderVTab;

// Helper function to check file accessibility and safety limits
fn check_file_access(file_path: &str) -> Result<(), JsonReaderError> {
    // Check if file exists and is readable
    let path = Path::new(file_path);
    if !path.exists() {
        return Err(JsonReaderError::IoError(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            format!("File does not exist: {}", file_path)
        )));
    }

    // Check file size for safety (limit to 100MB for now)
    const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024; // 100MB
    let metadata = std::fs::metadata(path)?;
    let file_size = metadata.len();

    if file_size > MAX_FILE_SIZE {
        return Err(JsonReaderError::InvalidStructure(
            format!("File too large: {} bytes (max: {} bytes)", file_size, MAX_FILE_SIZE)
        ));
    }

    // Try to open the file to check permissions
    let _file = File::open(file_path)?;

    Ok(())
}

// Helper function to safely create CString with proper error handling
fn safe_cstring(s: &str) -> Result<CString, JsonReaderError> {
    // Replace null bytes and other problematic characters
    let cleaned = s.replace('\0', "\\0");
    CString::new(cleaned).map_err(|e| JsonReaderError::StateError(format!("CString creation failed: {}", e)))
}





// Helper function to read JSON structure and return as table
fn read_json_as_table(
    file_path: &str,
    columns: &[String],
    _init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, JsonReaderError> {
    // Basic file access check
    check_file_access(file_path)?;

    // Read and validate JSON using struson
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Validate JSON by attempting to parse it
    // This will catch malformed JSON and throw proper errors
    let mut content = String::new();

    // Read the entire JSON structure to validate it
    match json_reader.peek()? {
        struson::reader::ValueType::Object => {
            // Read the entire object as JSON string
            json_reader.skip_value()?;
        }
        struson::reader::ValueType::Array => {
            // Read the entire array as JSON string
            json_reader.skip_value()?;
        }
        _ => {
            // Read primitive value
            json_reader.skip_value()?;
        }
    }

    // If we get here, JSON is valid. Now read the file as string for return
    use std::io::Read;
    let mut file = File::open(file_path)?;
    file.read_to_string(&mut content)?;

    let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];

    // Put the entire JSON content in the first column
    if !columns.is_empty() {
        result_columns[0].push(content);
    }

    Ok(result_columns)
}







impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // For now, just add a single generic column
        // We'll discover the actual structure during execution
        let columns = vec!["json_data".to_string()];

        // Add the column to DuckDB
        bind.add_result_column("json_data", LogicalTypeHandle::from(LogicalTypeId::Varchar));

        Ok(JsonReaderBindData {
            file_path,
            json_path: None,
            columns,
        })
    }

    fn init(_init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Simple initialization - no complex path inference for now
        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // Use the new generic JSON reader
        match read_json_as_table(&bind_data.file_path, &bind_data.columns, init_data) {
            Ok(rows) => {
                if rows.is_empty() {
                    init_data.finished.store(true, Ordering::Relaxed);
                    output.set_len(0);
                } else {
                    // Fill the output vectors with the row data
                    for (col_idx, column_data) in rows.iter().enumerate() {
                        let vector = output.flat_vector(col_idx);
                        for (row_idx, value) in column_data.iter().enumerate() {
                            // Use safe CString creation to prevent crashes
                            match safe_cstring(value.as_str()) {
                                Ok(cstring) => vector.insert(row_idx, cstring),
                                Err(_) => {
                                    // If CString creation fails, use a safe fallback
                                    let safe_value = value.replace('\0', "\\0");
                                    if let Ok(cstring) = CString::new(safe_value) {
                                        vector.insert(row_idx, cstring);
                                    } else {
                                        // Last resort: empty string
                                        vector.insert(row_idx, CString::new("").unwrap());
                                    }
                                }
                            }
                        }
                    }
                    output.set_len(rows[0].len());
                    // Mark as finished since we read everything in one go
                    init_data.finished.store(true, Ordering::Relaxed);
                }
            }
            Err(e) => {
                // All errors should fail the query, not return error data as rows
                init_data.finished.store(true, Ordering::Relaxed);
                return Err(format!("JSON Reader Error: {}", e).into());
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}



#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}