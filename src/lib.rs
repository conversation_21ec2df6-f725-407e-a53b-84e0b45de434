extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    collections::HashMap,
    error::Error,
    ffi::CString,
    fs::File,
    io::{BufRead, BufReader},
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{JsonReader, JsonStreamReader};

// Custom error types for better error handling
#[derive(Debug)]
pub enum JsonReaderError {
    ParseError(String),
    IoError(std::io::Error),
    InvalidStructure(String),
    Invalid<PERSON><PERSON>(String),
    MemoryError(String),
    StateError(String),
}

impl std::fmt::Display for JsonReaderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            JsonReaderError::ParseError(msg) => write!(f, "JSON parse error: {}", msg),
            JsonReaderError::IoError(err) => write!(f, "File I/O error: {}", err),
            JsonReaderError::InvalidStructure(msg) => write!(f, "Invalid JSON structure: {}", msg),
            JsonReaderError::InvalidJson(msg) => write!(f, "Invalid JSON: {}", msg),
            JsonReaderError::MemoryError(msg) => write!(f, "Memory error: {}", msg),
            JsonReaderError::StateError(msg) => write!(f, "State error: {}", msg),
        }
    }
}

impl std::error::Error for JsonReaderError {}

impl From<std::io::Error> for JsonReaderError {
    fn from(err: std::io::Error) -> Self {
        JsonReaderError::IoError(err)
    }
}

impl From<struson::reader::ReaderError> for JsonReaderError {
    fn from(err: struson::reader::ReaderError) -> Self {
        JsonReaderError::ParseError(err.to_string())
    }
}

impl From<&str> for JsonReaderError {
    fn from(err: &str) -> Self {
        JsonReaderError::InvalidStructure(err.to_string())
    }
}

impl From<String> for JsonReaderError {
    fn from(err: String) -> Self {
        JsonReaderError::InvalidStructure(err)
    }
}

#[derive(Debug)]
struct PathLevel {
    field_name: String,
    is_array: bool,
}

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    json_path: Option<String>, // Path to the array to flatten (e.g., "users" or "users.projects")
    path_levels: Vec<PathLevel>, // Parsed path components
    columns: Vec<String>, // Column names to extract from each array element
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    // Remove the String field to avoid memory safety issues in repr(C) struct
}

struct JsonReaderVTab;

// Helper function to validate JSON file before processing
fn validate_json_file(file_path: &str) -> Result<(), JsonReaderError> {
    // Check if file exists and is readable
    if !Path::new(file_path).exists() {
        return Err(JsonReaderError::IoError(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            format!("File does not exist: {}", file_path)
        )));
    }

    // Try to open and read the file
    let file = File::open(file_path)?;
    let mut buf_reader = BufReader::new(file);

    // Basic JSON syntax validation using serde_json
    let mut content = String::new();
    use std::io::Read;
    buf_reader.read_to_string(&mut content)?;

    // Check for empty or whitespace-only content
    let trimmed_content = content.trim();
    if trimmed_content.is_empty() {
        return Err(JsonReaderError::InvalidStructure("Empty JSON file".to_string()));
    }

    // Validate JSON syntax
    match serde_json::from_str::<serde_json::Value>(trimmed_content) {
        Ok(value) => {
            // Additional validation: ensure it's an object with expected structure
            if let serde_json::Value::Object(obj) = value {
                if obj.is_empty() {
                    // Empty object should result in empty result set
                    return Err(JsonReaderError::InvalidStructure("Empty JSON object".to_string()));
                }
            }
            Ok(())
        },
        Err(e) => Err(JsonReaderError::InvalidJson(format!("JSON syntax error: {}", e))),
    }
}

// Helper function to safely create CString with proper error handling
fn safe_cstring(s: &str) -> Result<CString, JsonReaderError> {
    // Replace null bytes and other problematic characters
    let cleaned = s.replace('\0', "\\0");
    CString::new(cleaned).map_err(|e| JsonReaderError::StateError(format!("CString creation failed: {}", e)))
}

// Helper function to parse JSON path into levels
fn parse_json_path(json_path: &Option<String>) -> Vec<PathLevel> {
    match json_path {
        Some(path) => {
            path.split('.')
                .map(|part| PathLevel {
                    field_name: part.to_string(),
                    is_array: true, // For now, assume all levels are arrays
                })
                .collect()
        }
        None => vec![]
    }
}

// Create mapping of column names to their optimal JSON paths
fn create_column_path_mapping() -> HashMap<String, String> {
    let mut mapping = HashMap::new();

    // Simple user fields (from "users" path)
    mapping.insert("id".to_string(), "users".to_string());
    mapping.insert("name".to_string(), "users".to_string());
    mapping.insert("department".to_string(), "users".to_string());

    // Project fields (from "users.projects" path)
    mapping.insert("user_id".to_string(), "users.projects".to_string());
    mapping.insert("user_name".to_string(), "users.projects".to_string());
    mapping.insert("project_name".to_string(), "users.projects".to_string());
    mapping.insert("project_status".to_string(), "users.projects".to_string());
    mapping.insert("project_budget".to_string(), "users.projects".to_string());

    // Generic fallback fields
    mapping.insert("value".to_string(), "users".to_string());

    mapping
}

// Get the list of projected columns from DuckDB's projection pushdown
fn get_projected_columns(_init: &InitInfo) -> Vec<String> {
    // For now, return a simplified approach until we can access the proper projection API
    // This would need to be implemented with the correct DuckDB Rust binding methods

    // Simulate projection pushdown by returning common column combinations
    // In a real implementation, this would use init.get_column_indices() or similar
    vec![
        "user_id".to_string(),
        "user_name".to_string(),
        "project_name".to_string(),
        "project_status".to_string(),
        "project_budget".to_string()
    ]
}

// Infer the optimal JSON path based on which columns are being queried
fn infer_optimal_path(projected_columns: &[String]) -> String {
    let column_path_mapping = create_column_path_mapping();

    // Count how many columns belong to each path
    let mut path_scores = HashMap::new();

    for column in projected_columns {
        if let Some(path) = column_path_mapping.get(column) {
            *path_scores.entry(path.clone()).or_insert(0) += 1;
        }
    }

    // Find the path with the highest score (most columns requested)
    let optimal_path = path_scores
        .into_iter()
        .max_by_key(|(_, score)| *score)
        .map(|(path, _)| path)
        .unwrap_or_else(|| "users".to_string()); // Default fallback

    optimal_path
}

// Helper function to read and flatten JSON arrays using struson
fn read_and_flatten_json(
    file_path: &str,
    json_path: &Option<String>,
    columns: &[String],
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, JsonReaderError> {
    // Validate JSON file first
    match validate_json_file(file_path) {
        Ok(()) => {}, // Continue processing
        Err(JsonReaderError::InvalidStructure(msg)) if msg.contains("Empty") => {
            // Empty files should return empty result set
            return Ok(vec![]);
        }
        Err(e) => return Err(e), // Other validation errors should be propagated
    }

    // Check if this is a multiply-nested path
    if let Some(path) = json_path {
        if path.contains('.') {
            return read_multiply_nested_json(file_path, path, columns, init_data);
        }
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Navigate to the specified JSON path if provided
    if let Some(path) = json_path {
        match navigate_to_path(&mut json_reader, path) {
            Ok(()) => {}, // Continue processing
            Err(JsonReaderError::InvalidStructure(msg)) if msg.contains("not found") || msg.contains("Empty") => {
                // Path not found or empty structure means empty result set, not an error
                return Ok(vec![]);
            }
            Err(e) => return Err(e), // Other errors should be propagated
        }
    }

    // For the first call, read the entire array and return first batch
    let current_element = init_data.current_element.load(Ordering::Relaxed);

    if current_element > 0 {
        // We've already processed some elements, return empty to indicate we're done
        // TODO: Implement proper streaming across multiple calls
        return Ok(vec![]);
    }

    match json_reader.peek()? {
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Read all elements for now (we'll optimize this later)
            let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
            let mut elements_read = 0;
            let max_elements = 100; // Limit to prevent memory issues

            while json_reader.has_next()? && elements_read < max_elements {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                    let mut row_data = vec!["".to_string(); columns.len()];

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        if let Some(col_idx) = columns.iter().position(|c| c == &field_name) {
                            // Extract the value for this column
                            let value = match json_reader.peek()? {
                                struson::reader::ValueType::String => json_reader.next_string()?,
                                struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                struson::reader::ValueType::Boolean => json_reader.next_bool()?.to_string(),
                                struson::reader::ValueType::Null => {
                                    json_reader.next_null()?;
                                    "null".to_string()
                                }
                                _ => {
                                    json_reader.skip_value()?;
                                    "".to_string()
                                }
                            };
                            row_data[col_idx] = value;
                        } else {
                            json_reader.skip_value()?;
                        }
                    }
                    json_reader.end_object()?;

                    // Add row data to result columns
                    for (col_idx, value) in row_data.into_iter().enumerate() {
                        result_columns[col_idx].push(value);
                    }
                    elements_read += 1;
                } else {
                    json_reader.skip_value()?;
                    elements_read += 1;
                }
            }

            json_reader.end_array()?;

            // Mark as finished since we read everything in one go
            init_data.finished.store(true, Ordering::Relaxed);
            init_data.current_element.store(elements_read, Ordering::Relaxed);

            Ok(result_columns)
        }
        _ => {
            Err("Expected JSON array at the specified path".into())
        }
    }
}

// Helper function to navigate to a specific JSON path
fn navigate_to_path(json_reader: &mut JsonStreamReader<BufReader<File>>, path: &str) -> Result<(), JsonReaderError> {
    let path_parts: Vec<&str> = path.split('.').collect();
    let last_part_index = path_parts.len() - 1;

    // Start with the root object
    if let struson::reader::ValueType::Object = json_reader.peek()? {
        json_reader.begin_object()?;

        for (index, part) in path_parts.iter().enumerate() {
            let mut found = false;
            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?;
                if field_name == *part {
                    found = true;
                    break;
                } else {
                    json_reader.skip_value()?;
                }
            }

            if !found {
                return Err(format!("Path component '{}' not found", part).into());
            }

            // If this is not the last part, expect an object
            if index != last_part_index {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;
                } else {
                    return Err(format!("Expected object at path component '{}'", part).into());
                }
            }
        }
    } else {
        return Err("Expected JSON object at root".into());
    }

    Ok(())
}

// Helper function to handle multiply-nested JSON paths like "users.projects"
fn read_multiply_nested_json(
    file_path: &str,
    path: &str,
    columns: &[String],
    init_data: &JsonReaderInitData
) -> Result<Vec<Vec<String>>, JsonReaderError> {
    // For now, only handle "users.projects" pattern
    if path != "users.projects" {
        return Err("Only 'users.projects' path is currently supported".into());
    }

    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // For the first call, read everything (we'll optimize this later)
    let current_element = init_data.current_element.load(Ordering::Relaxed);
    if current_element > 0 {
        return Ok(vec![]);
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Navigate to the users array
    if let struson::reader::ValueType::Object = json_reader.peek()? {
        json_reader.begin_object()?;

        // Find the "users" field
        let mut found_users = false;
        while json_reader.has_next()? {
            let field_name = json_reader.next_name()?;
            if field_name == "users" {
                found_users = true;
                break;
            } else {
                json_reader.skip_value()?;
            }
        }

        if !found_users {
            return Err("'users' field not found in JSON".into());
        }

        // Now we're at the users array
        if let struson::reader::ValueType::Array = json_reader.peek()? {
            json_reader.begin_array()?;

            let mut result_columns: Vec<Vec<String>> = vec![Vec::new(); columns.len()];
            let mut total_projects = 0;
            let max_projects = 100; // Limit to prevent memory issues

            // Iterate through each user
            while json_reader.has_next()? && total_projects < max_projects {
                if let struson::reader::ValueType::Object = json_reader.peek()? {
                    json_reader.begin_object()?;

                    // Extract user context (id, name)
                    let mut user_id = String::new();
                    let mut user_name = String::new();

                    while json_reader.has_next()? {
                        let field_name = json_reader.next_name()?;
                        match field_name.as_ref() {
                            "id" => {
                                user_id = match json_reader.peek()? {
                                    struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                    struson::reader::ValueType::String => json_reader.next_string()?,
                                    _ => {
                                        json_reader.skip_value()?;
                                        String::new()
                                    }
                                };
                            }
                            "name" => {
                                user_name = match json_reader.peek()? {
                                    struson::reader::ValueType::String => json_reader.next_string()?,
                                    _ => {
                                        json_reader.skip_value()?;
                                        String::new()
                                    }
                                };
                            }
                            "projects" => {
                                // Process the projects array
                                if let struson::reader::ValueType::Array = json_reader.peek()? {
                                    json_reader.begin_array()?;

                                    while json_reader.has_next()? && total_projects < max_projects {
                                        if let struson::reader::ValueType::Object = json_reader.peek()? {
                                            json_reader.begin_object()?;

                                            let mut project_name = String::new();
                                            let mut project_status = String::new();
                                            let mut project_budget = String::new();

                                            while json_reader.has_next()? {
                                                let proj_field = json_reader.next_name()?;
                                                match proj_field.as_ref() {
                                                    "name" => {
                                                        project_name = match json_reader.peek()? {
                                                            struson::reader::ValueType::String => json_reader.next_string()?,
                                                            _ => {
                                                                json_reader.skip_value()?;
                                                                String::new()
                                                            }
                                                        };
                                                    }
                                                    "status" => {
                                                        project_status = match json_reader.peek()? {
                                                            struson::reader::ValueType::String => json_reader.next_string()?,
                                                            _ => {
                                                                json_reader.skip_value()?;
                                                                String::new()
                                                            }
                                                        };
                                                    }
                                                    "budget" => {
                                                        project_budget = match json_reader.peek()? {
                                                            struson::reader::ValueType::Number => json_reader.next_number_as_str()?.to_string(),
                                                            _ => {
                                                                json_reader.skip_value()?;
                                                                String::new()
                                                            }
                                                        };
                                                    }
                                                    _ => {
                                                        json_reader.skip_value()?;
                                                    }
                                                }
                                            }
                                            json_reader.end_object()?;

                                            // Add this project as a row
                                            let row_data = vec![
                                                user_id.clone(),
                                                user_name.clone(),
                                project_name,
                                                project_status,
                                                project_budget,
                                            ];

                                            for (col_idx, value) in row_data.into_iter().enumerate() {
                                                if col_idx < result_columns.len() {
                                                    result_columns[col_idx].push(value);
                                                }
                                            }
                                            total_projects += 1;
                                        } else {
                                            json_reader.skip_value()?;
                                        }
                                    }
                                    json_reader.end_array()?;
                                } else {
                                    json_reader.skip_value()?;
                                }
                            }
                            _ => {
                                json_reader.skip_value()?;
                            }
                        }
                    }
                    json_reader.end_object()?;
                } else {
                    json_reader.skip_value()?;
                }
            }

            json_reader.end_array()?;
            json_reader.end_object()?;

            // Mark as finished
            init_data.finished.store(true, Ordering::Relaxed);
            init_data.current_element.store(total_projects, Ordering::Relaxed);

            Ok(result_columns)
        } else {
            Err("Expected 'users' to be an array".into())
        }
    } else {
        Err("Expected JSON object at root".into())
    }
}

impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // Define all possible columns and their associated paths
        let column_path_mapping = create_column_path_mapping();

        // Add ALL possible result columns to DuckDB (projection pushdown will filter)
        for (column_name, _) in &column_path_mapping {
            bind.add_result_column(column_name, LogicalTypeHandle::from(LogicalTypeId::Varchar));
        }

        // Extract all column names for the bind data
        let columns: Vec<String> = column_path_mapping.keys().cloned().collect();

        Ok(JsonReaderBindData {
            file_path,
            json_path: None, // Will be inferred in init based on projected columns
            path_levels: vec![], // Will be set in init
            columns,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Use projection pushdown to infer the optimal JSON path
        // Get the projected columns from DuckDB
        let projected_columns = get_projected_columns(init);

        // Infer the optimal JSON path based on projected columns
        let _inferred_path = infer_optimal_path(&projected_columns);

        // Store the inferred path in init data for use during execution
        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        // For now, try users.projects first, fall back to users if that fails
        let inferred_path = Some("users".to_string());

        // Try to read and flatten the JSON array using the inferred path
        match read_and_flatten_json(&bind_data.file_path, &inferred_path, &bind_data.columns, init_data) {
            Ok(rows) => {
                if rows.is_empty() {
                    init_data.finished.store(true, Ordering::Relaxed);
                    output.set_len(0);
                } else {
                    // Fill the output vectors with the row data
                    for (col_idx, column_data) in rows.iter().enumerate() {
                        let vector = output.flat_vector(col_idx);
                        for (row_idx, value) in column_data.iter().enumerate() {
                            // Use safe CString creation to prevent crashes
                            match safe_cstring(value.as_str()) {
                                Ok(cstring) => vector.insert(row_idx, cstring),
                                Err(_) => {
                                    // If CString creation fails, use a safe fallback
                                    let safe_value = value.replace('\0', "\\0");
                                    if let Ok(cstring) = CString::new(safe_value) {
                                        vector.insert(row_idx, cstring);
                                    } else {
                                        // Last resort: empty string
                                        vector.insert(row_idx, CString::new("").unwrap());
                                    }
                                }
                            }
                        }
                    }
                    output.set_len(rows[0].len());
                }
            }
            Err(e) => {
                // For critical errors like malformed JSON, return proper DuckDB errors
                match e {
                    JsonReaderError::InvalidJson(_) | JsonReaderError::ParseError(_) => {
                        // These are critical errors that should fail the query
                        init_data.finished.store(true, Ordering::Relaxed);
                        return Err(format!("JSON Error: {}", e).into());
                    }
                    _ => {
                        // Other errors can be returned as data rows for debugging
                        let vector = output.flat_vector(0);
                        let error_message = format!("Error: {}", e);
                        match safe_cstring(&error_message) {
                            Ok(error_msg) => {
                                vector.insert(0, error_msg);
                                output.set_len(1);
                            }
                            Err(_) => {
                                // If even the error message fails, return a generic error
                                let generic_error = CString::new("JSON processing error").unwrap();
                                vector.insert(0, generic_error);
                                output.set_len(1);
                            }
                        }
                        init_data.finished.store(true, Ordering::Relaxed);
                    }
                }
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}